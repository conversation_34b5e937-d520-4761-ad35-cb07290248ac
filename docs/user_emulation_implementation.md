# User Emulation API Implementation & Caching

## Overview

This document provides a comprehensive implementation guide for adding user emulation functionality to the StoreTrack app. When a user is selected from the emulate list, the app will call 10 specific APIs using the selected user's ID and **overwrite** the existing cache with the emulated user's data. This provides a seamless "switch user context" experience where the app continues working normally but displays the emulated user's data instead of the current user's data.

## Current API Status Analysis

### ✅ Already Cached APIs (4/10)
| API Endpoint | Method | Realm Model | Status |
|--------------|--------|-------------|---------|
| `/tasks_optimize` | getTasks | `TaskDetailModel` | ✅ Cached |
| `/update_pos` | updatePos | `PosResponseItemModel` | ✅ Cached |
| `/misc_setting` | getMiscSetting | `MiscSettingModel` | ✅ Cached |
| `/profile_postal` | getProfile | `ProfileModel` | ✅ Cached |

### ❌ Missing Cache Implementation (6/10)
| API Endpoint | Method | Response Model | New Realm Model Needed |
|--------------|--------|----------------|----------------------|
| `/history` | getHistory | `HistoryResponse` | `HistoryModel` |
| `/alert` | getAlerts | `NotificationResponse` | `AlertModel` |
| `/availability_multi` | getAvailability | `AvailabilityResponse` | `AvailabilityModel` |
| `/leave` | getLeave | `LeaveResponse` | `LeaveModel` |
| `/skill` | getSkills | `SkillsResponse` | `SkillsModel` |
| `/induction` | getInduction | `InductionResponse` | `InductionModel` |

## Implementation Plan

### Phase 1: Create Missing Realm Models

#### 1.1 HistoryModel
**File**: `lib/features/home/<USER>/models/history_model.dart`

```dart
import 'package:realm/realm.dart';

part 'history_model.realm.dart';

@RealmModel()
class _HistoryModel {
  @PrimaryKey()
  late String id; // "history"
  
  late String jsonData; // Serialized HistoryResponse JSON
  late DateTime lastUpdated;
  late DateTime createdAt;
}
```

#### 1.2 AlertModel
**File**: `lib/features/home/<USER>/models/alert_model.dart`

```dart
import 'package:realm/realm.dart';

part 'alert_model.realm.dart';

@RealmModel()
class _AlertModel {
  @PrimaryKey()
  late String id; // "alerts"
  
  late String jsonData; // Serialized NotificationResponse JSON
  late DateTime lastUpdated;
  late DateTime createdAt;
}
```

#### 1.3 AvailabilityModel
**File**: `lib/features/home/<USER>/models/availability_model.dart`

```dart
import 'package:realm/realm.dart';

part 'availability_model.realm.dart';

@RealmModel()
class _AvailabilityModel {
  @PrimaryKey()
  late String id; // "availability"
  
  late String jsonData; // Serialized AvailabilityResponse JSON
  late DateTime lastUpdated;
  late DateTime createdAt;
}
```

#### 1.4 LeaveModel
**File**: `lib/features/home/<USER>/models/leave_model.dart`

```dart
import 'package:realm/realm.dart';

part 'leave_model.realm.dart';

@RealmModel()
class _LeaveModel {
  @PrimaryKey()
  late String id; // "leave"
  
  late String jsonData; // Serialized LeaveResponse JSON
  late DateTime lastUpdated;
  late DateTime createdAt;
}
```

#### 1.5 SkillsModel
**File**: `lib/features/home/<USER>/models/skills_model.dart`

```dart
import 'package:realm/realm.dart';

part 'skills_model.realm.dart';

@RealmModel()
class _SkillsModel {
  @PrimaryKey()
  late String id; // "skills"
  
  late String jsonData; // Serialized SkillsResponse JSON
  late DateTime lastUpdated;
  late DateTime createdAt;
}
```

#### 1.6 InductionModel
**File**: `lib/features/home/<USER>/models/induction_model.dart`

```dart
import 'package:realm/realm.dart';

part 'induction_model.realm.dart';

@RealmModel()
class _InductionModel {
  @PrimaryKey()
  late String id; // "induction"
  
  late String jsonData; // Serialized InductionResponse JSON
  late DateTime lastUpdated;
  late DateTime createdAt;
}
```

### Phase 2: Create Mapper Classes

#### 2.1 HistoryMapper
**File**: `lib/features/home/<USER>/mappers/history_mapper.dart`

```dart
import 'dart:convert';
import '../models/history_model.dart';
import '../models/history_response.dart';

class HistoryMapper {
  static HistoryModel toModel(HistoryResponse response) {
    final model = HistoryModel()
      ..id = "history"
      ..jsonData = jsonEncode(response.toJson())
      ..lastUpdated = DateTime.now()
      ..createdAt = DateTime.now();
    
    return model;
  }

  static HistoryResponse toResponse(HistoryModel model) {
    final jsonData = jsonDecode(model.jsonData) as Map<String, dynamic>;
    return HistoryResponse.fromJson(jsonData);
  }

  static HistoryModel updateModel({
    required HistoryModel existingModel,
    required HistoryResponse response,
  }) {
    return existingModel
      ..jsonData = jsonEncode(response.toJson())
      ..lastUpdated = DateTime.now();
  }
}
```

#### 2.2 Similar Mappers for Other Models
Follow the same pattern for:
- `AlertMapper` 
- `AvailabilityMapper`
- `LeaveMapper`
- `SkillsMapper`
- `InductionMapper`

### Phase 3: Update Database Schema

#### 3.1 Update RealmDatabase
**File**: `lib/core/database/realm_database.dart`

```dart
// Add imports
import 'package:storetrack_app/features/home/<USER>/models/history_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/alert_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/availability_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/leave_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/skills_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/induction_model.dart';

// Update schema configuration
final config = Configuration.local(
  [
    // ... existing schemas
    
    // Add new schemas
    HistoryModel.schema,
    AlertModel.schema,
    AvailabilityModel.schema,
    LeaveModel.schema,
    SkillsModel.schema,
    InductionModel.schema,
  ],
  schemaVersion: 12, // Increment from 11 to 12
);

// Update clearAllData method
void clearAllData() {
  _realm.write(() {
    // ... existing deletions
    
    // Add new model deletions
    _realm.deleteAll<HistoryModel>();
    _realm.deleteAll<AlertModel>();
    _realm.deleteAll<AvailabilityModel>();
    _realm.deleteAll<LeaveModel>();
    _realm.deleteAll<SkillsModel>();
    _realm.deleteAll<InductionModel>();
  });
}
```

### Phase 4: Update Data Sources

#### 4.1 Update HomeLocalDataSource
**File**: `lib/features/home/<USER>/datasources/home_local_datasource.dart`

```dart
// Add new methods for caching
abstract class HomeLocalDataSource {
  // ... existing methods
  
  // History methods
  Future<void> saveHistory(HistoryResponse response);
  Future<HistoryResponse?> getCachedHistory();
  
  // Alert methods
  Future<void> saveAlerts(NotificationResponse response);
  Future<NotificationResponse?> getCachedAlerts();
  
  // Availability methods
  Future<void> saveAvailability(AvailabilityResponse response);
  Future<AvailabilityResponse?> getCachedAvailability();
  
  // Leave methods
  Future<void> saveLeave(LeaveResponse response);
  Future<LeaveResponse?> getCachedLeave();
  
  // Skills methods
  Future<void> saveSkills(SkillsResponse response);
  Future<SkillsResponse?> getCachedSkills();
  
  // Induction methods
  Future<void> saveInduction(InductionResponse response);
  Future<InductionResponse?> getCachedInduction();
}

// Implementation
class HomeLocalDataSourceImpl implements HomeLocalDataSource {
  final RealmDatabase _realmDatabase;
  
  HomeLocalDataSourceImpl({required RealmDatabase realmDatabase})
      : _realmDatabase = realmDatabase;

  // History implementation
  @override
  Future<void> saveHistory(HistoryResponse response) async {
    final realm = _realmDatabase.realm;
    final existModel = realm.find<HistoryModel>("history");
    
    realm.write(() {
      if (existModel != null) {
        HistoryMapper.updateModel(
          existingModel: existModel,
          response: response,
        );
      } else {
        final newModel = HistoryMapper.toModel(response);
        realm.add(newModel);
      }
    });
  }

  @override
  Future<HistoryResponse?> getCachedHistory() async {
    final realm = _realmDatabase.realm;
    final model = realm.find<HistoryModel>("history");
    
    if (model != null) {
      return HistoryMapper.toResponse(model);
    }
    return null;
  }
  
  // Similar implementations for other 5 APIs...
}
```

### Phase 5: Update Use Cases

#### 5.1 Update GetHistoryUseCase
**File**: `lib/features/home/<USER>/usecases/get_history_usecase.dart`

```dart
class GetHistoryUseCase {
  final HomeRepository _repository;

  GetHistoryUseCase(this._repository);

  Future<Result<HistoryResponse>> call({
    required String token,
    required String userId, // This will be the target user's ID (current or emulated)
    bool isSync = false,
  }) async {
    // Try cache first if not syncing
    if (!isSync) {
      final cached = await _repository.getCachedHistory();
      if (cached != null) {
        return Result.success(cached);
      }
    }
    
    // Fetch from remote using the provided userId
    final result = await _repository.getHistory(
      token: token,
      userId: userId,
    );
    
    // Cache the result if successful (overwrites existing cache)
    if (result.isSuccess && result.data != null) {
      await _repository.saveHistory(result.data!);
    }
    
    return result;
  }
}
```

### Phase 6: Update EmulateUserCubit

#### 6.1 Update EmulateUserState
**File**: `lib/features/home/<USER>/blocs/emulate_user/emulate_user_state.dart`

```dart
// Add new states
class EmulateUserEmulating extends EmulateUserState {
  final EmulateUserEntity user;
  final String currentApi;
  final int progress; // 0-10

  const EmulateUserEmulating({
    required this.user,
    required this.currentApi,
    required this.progress,
  });

  @override
  List<Object> get props => [user, currentApi, progress];
}

class EmulateUserEmulationSuccess extends EmulateUserState {
  final EmulateUserEntity user;
  final Map<String, bool> apiResults; // API name -> success/failure

  const EmulateUserEmulationSuccess({
    required this.user,
    required this.apiResults,
  });

  @override
  List<Object> get props => [user, apiResults];
}

class EmulateUserEmulationError extends EmulateUserState {
  final String message;
  final EmulateUserEntity? user;

  const EmulateUserEmulationError(this.message, {this.user});

  @override
  List<Object?> get props => [message, user];
}
```

#### 6.2 Update EmulateUserCubit
**File**: `lib/features/home/<USER>/blocs/emulate_user/emulate_user_cubit.dart`

```dart
class EmulateUserCubit extends Cubit<EmulateUserState> {
  // ... existing code

  Future<void> emulateUser(EmulateUserEntity user) async {
    if (user.userId == null) {
      emit(const EmulateUserEmulationError('Invalid user ID'));
      return;
    }

    final emulatedUserId = user.userId.toString();
    final dataManager = sl<DataManager>();
    final token = await dataManager.getAuthToken() ?? '';
    
    if (token.isEmpty) {
      emit(const EmulateUserEmulationError('Authentication token not found'));
      return;
    }

    final apiResults = <String, bool>{};
    
    try {
      // API 1: getTasks - Call with emulated user ID, cache will be overwritten
      emit(EmulateUserEmulating(
        user: user,
        currentApi: 'Getting Tasks',
        progress: 1,
      ));
      
      final tasksResult = await sl<GetTasksUseCase>().call(
        TasksRequestEntity(
          deviceUid: await dataManager.getOrCreateDeviceId(),
          userId: emulatedUserId, // Use emulated user's ID
          appversion: AppConstants.appVersion,
          tasks: const [],
          token: token,
        ),
      );
      apiResults['getTasks'] = tasksResult.isSuccess;

      // API 2: updatePos
      emit(EmulateUserEmulating(
        user: user,
        currentApi: 'Updating POS',
        progress: 2,
      ));
      
      final posResult = await sl<UpdatePosUseCase>().call(
        // Create request using emulated user ID
        userId: emulatedUserId,
        token: token,
      );
      apiResults['updatePos'] = posResult.isSuccess;

      // API 3: getHistory - Cache will be overwritten with emulated user's data
      emit(EmulateUserEmulating(
        user: user,
        currentApi: 'Getting History',
        progress: 3,
      ));
      
      final historyResult = await sl<GetHistoryUseCase>().call(
        token: token,
        userId: emulatedUserId, // Use emulated user's ID
      );
      apiResults['getHistory'] = historyResult.isSuccess;

      // API 4: getMiscSetting
      emit(EmulateUserEmulating(
        user: user,
        currentApi: 'Getting Settings',
        progress: 4,
      ));
      
      final miscResult = await sl<GetMiscSettingUseCase>().call(
        token: token,
        userId: emulatedUserId, // Use emulated user's ID
      );
      apiResults['getMiscSetting'] = miscResult.isSuccess;

      // API 5: getAlerts
      emit(EmulateUserEmulating(
        user: user,
        currentApi: 'Getting Alerts',
        progress: 5,
      ));
      
      final alertsResult = await sl<GetAlertsUseCase>().call(
        token: token,
        userId: emulatedUserId, // Use emulated user's ID
      );
      apiResults['getAlerts'] = alertsResult.isSuccess;

      // API 6: getProfile
      emit(EmulateUserEmulating(
        user: user,
        currentApi: 'Getting Profile',
        progress: 6,
      ));
      
      final profileResult = await sl<GetProfileUseCase>().call(
        token: token,
        userId: emulatedUserId, // Use emulated user's ID
      );
      apiResults['getProfile'] = profileResult.isSuccess;

      // API 7: getAvailability
      emit(EmulateUserEmulating(
        user: user,
        currentApi: 'Getting Availability',
        progress: 7,
      ));
      
      final availabilityResult = await sl<GetAvailabilityUseCase>().call(
        token: token,
        userId: emulatedUserId, // Use emulated user's ID
      );
      apiResults['getAvailability'] = availabilityResult.isSuccess;

      // API 8: getLeave
      emit(EmulateUserEmulating(
        user: user,
        currentApi: 'Getting Leave',
        progress: 8,
      ));
      
      final leaveResult = await sl<GetLeaveUseCase>().call(
        token: token,
        userId: emulatedUserId, // Use emulated user's ID
      );
      apiResults['getLeave'] = leaveResult.isSuccess;

      // API 9: getSkills
      emit(EmulateUserEmulating(
        user: user,
        currentApi: 'Getting Skills',
        progress: 9,
      ));
      
      final skillsResult = await sl<GetSkillsUseCase>().call(
        token: token,
        userId: emulatedUserId, // Use emulated user's ID
      );
      apiResults['getSkills'] = skillsResult.isSuccess;

      // API 10: getInduction
      emit(EmulateUserEmulating(
        user: user,
        currentApi: 'Getting Induction',
        progress: 10,
      ));
      
      final inductionResult = await sl<GetInductionUseCase>().call(
        token: token,
        userId: emulatedUserId, // Use emulated user's ID
      );
      apiResults['getInduction'] = inductionResult.isSuccess;

      // All successful API calls have overwritten the cache with emulated user's data
      emit(EmulateUserEmulationSuccess(
        user: user,
        apiResults: apiResults,
      ));
      
    } catch (e) {
      emit(EmulateUserEmulationError(
        'Error during user emulation: $e',
        user: user,
      ));
    }
  }
}
```

### Phase 7: Update UI

#### 7.1 Update EmulateUserPage
**File**: `lib/features/home/<USER>/pages/emulate_user_page.dart`

```dart
void _onUserSelected(EmulateUserEntity user) {
  // Start the emulation process
  context.read<EmulateUserCubit>().emulateUser(user);
}

// Update BlocConsumer listener
listener: (context, state) {
  if (state is EmulateUserError) {
    SnackBarService.error(
      context: context,
      message: state.message,
    );
  } else if (state is EmulateUserEmulationSuccess) {
    final successCount = state.apiResults.values.where((v) => v).length;
    final totalCount = state.apiResults.length;
    
    SnackBarService.success(
      context: context,
      message: 'Emulation completed: $successCount/$totalCount APIs successful',
    );
    
    // Optionally navigate or update UI
    // context.router.pushAndClearStack(DashboardRoute());
  } else if (state is EmulateUserEmulationError) {
    SnackBarService.error(
      context: context,
      message: state.message,
    );
  }
},

// Update builder for loading states
builder: (context, state) {
  if (state is EmulateUserEmulating) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            value: state.progress / 10.0,
            color: AppColors.primaryBlue,
          ),
          const SizedBox(height: 16),
          Text(
            state.currentApi,
            style: textTheme.montserratTitleSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Step ${state.progress} of 10',
            style: textTheme.montserratParagraphSmall.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
  
  // ... existing builder code
},
```

### Phase 8: Update SyncService

#### 8.1 Add Sync Methods
**File**: `lib/features/home/<USER>/services/sync_service.dart`

```dart
Future<void> sync() async {
  if (isSyncing.value) return;

  try {
    isSyncing.value = true;

    // ... existing sync operations

    // Add new sync operations
    await Future.wait([
      _getTasks(),
      _syncCalendar(),
      _syncProfile(),
      _syncMiscSetting(),
      // Add new sync methods
      _syncHistory(),
      _syncAlerts(),
      _syncAvailability(),
      _syncLeave(),
      _syncSkills(),
      _syncInduction(),
    ]);

    await sl<DataManager>().saveLastSyncTime(DateTime.now());
  } finally {
    isSyncing.value = false;
  }
}

// Add new sync methods
Future<void> _syncHistory() async {
  try {
    final token = await sl<DataManager>().getAuthToken() ?? '';
    final id = await sl<DataManager>().getUserId() ?? '';

    await sl<GetHistoryUseCase>().call(
      token: token,
      userId: id, // Use current user's ID for sync
      isSync: true,
    );
  } catch (e) {
    // Handle error appropriately
  }
}

Future<void> _syncAlerts() async {
  try {
    final token = await sl<DataManager>().getAuthToken() ?? '';
    final id = await sl<DataManager>().getUserId() ?? '';

    await sl<GetAlertsUseCase>().call(
      token: token,
      userId: id, // Use current user's ID for sync
      isSync: true,
    );
  } catch (e) {
    // Handle error appropriately
  }
}

// Similar methods for:
// _syncAvailability(), _syncLeave(), _syncSkills(), _syncInduction()
```

## Implementation Checklist

### Database Layer
- [ ] Create 6 new Realm model files
- [ ] Create 6 new mapper classes
- [ ] Update RealmDatabase schema to version 12
- [ ] Update clearAllData method
- [ ] Run `dart run build_runner build` to generate Realm files

### Data Layer
- [ ] Update HomeLocalDataSource with caching methods
- [ ] Update HomeRepository with new caching logic
- [ ] Update all 6 use cases with simplified caching
- [ ] Ensure use cases accept userId parameter for emulation

### Business Logic
- [ ] Update EmulateUserState with new states
- [ ] Add emulateUser method to EmulateUserCubit
- [ ] Handle individual API failures gracefully
- [ ] Provide progress feedback to UI

### UI Layer
- [ ] Update _onUserSelected method in EmulateUserPage
- [ ] Add loading states with progress indicator
- [ ] Handle success/error states appropriately
- [ ] Show API results summary to user

### Sync Integration
- [ ] Add 6 new sync methods to SyncService
- [ ] Integrate new sync methods with main sync flow
- [ ] Ensure sync restores current user's data (overwrites emulated data)

### Testing
- [ ] Test each new Realm model
- [ ] Test caching functionality for all 6 APIs
- [ ] Test emulation flow with different users
- [ ] Test error handling and recovery
- [ ] Test sync functionality restores current user data
- [ ] Test cache overwrite behavior during emulation

## File Structure Summary

```
lib/
├── core/database/
│   └── realm_database.dart                    [MODIFY - schema v12]
├── features/home/
│   ├── data/
│   │   ├── models/
│   │   │   ├── history_model.dart            [NEW]
│   │   │   ├── alert_model.dart              [NEW]
│   │   │   ├── availability_model.dart       [NEW]
│   │   │   ├── leave_model.dart              [NEW]
│   │   │   ├── skills_model.dart             [NEW]
│   │   │   └── induction_model.dart          [NEW]
│   │   ├── mappers/
│   │   │   ├── history_mapper.dart           [NEW]
│   │   │   ├── alert_mapper.dart             [NEW]
│   │   │   ├── availability_mapper.dart      [NEW]
│   │   │   ├── leave_mapper.dart             [NEW]
│   │   │   ├── skills_mapper.dart            [NEW]
│   │   │   └── induction_mapper.dart         [NEW]
│   │   ├── datasources/
│   │   │   └── home_local_datasource.dart    [MODIFY]
│   │   ├── repositories/
│   │   │   └── home_repository.dart          [MODIFY]
│   │   └── services/
│   │       └── sync_service.dart             [MODIFY]
│   ├── domain/usecases/
│   │   ├── get_history_usecase.dart          [MODIFY]
│   │   ├── get_alerts_usecase.dart           [MODIFY]
│   │   ├── get_availability_usecase.dart     [MODIFY]
│   │   ├── get_leave_usecase.dart            [MODIFY]
│   │   ├── get_skills_usecase.dart           [MODIFY]
│   │   └── get_induction_usecase.dart        [MODIFY]
│   └── presentation/
│       ├── blocs/emulate_user/
│       │   ├── emulate_user_cubit.dart       [MODIFY]
│       │   └── emulate_user_state.dart       [MODIFY]
│       └── pages/
│           └── emulate_user_page.dart        [MODIFY]
└── docs/
    └── user_emulation_implementation.md      [THIS FILE]
```

## Benefits

1. **Simplified Implementation**: Cache overwrite strategy is much easier to implement and maintain
2. **Complete Offline Support**: All emulated user data cached locally for offline access
3. **Improved Performance**: Faster access to cached data without user separation logic
4. **Seamless User Experience**: App works identically, just shows different user's data
5. **Error Resilience**: Graceful handling of individual API failures during emulation
6. **Clear Progress Feedback**: Step-by-step progress indicator during emulation process
7. **Maintainable Code**: Follows established patterns without complex user tracking
8. **Reduced Storage Usage**: Single cache per data type instead of multiple user caches
9. **Fast Development**: No need for complex user data separation or comparison features

## Notes

- Remember to run `dart run build_runner build` after creating Realm models
- Test each component individually before integration
- Consider implementing retry logic for failed API calls
- **Important**: Regular sync will restore current user's data by overwriting emulated data
- After emulation, user should sync to get their original data back
- Current user's data is lost during emulation (acceptable trade-off for simplified implementation)
- Consider adding a "Switch back to my data" button that triggers sync