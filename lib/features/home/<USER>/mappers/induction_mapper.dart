import 'dart:convert';
import '../models/cache_models.dart';
import '../models/induction_response.dart';

class InductionMapper {
  static InductionCacheModel toModel(InductionResponse response) {
    final now = DateTime.now();
    return InductionCacheModel(
      "induction",
      jsonEncode(response.toJson()),
      now,
      now,
    );
  }

  static InductionResponse toResponse(InductionCacheModel model) {
    final jsonData = jsonDecode(model.jsonData) as Map<String, dynamic>;
    return InductionResponse.fromJson(jsonData);
  }

  static InductionCacheModel updateModel({
    required InductionCacheModel existingModel,
    required InductionResponse response,
  }) {
    return existingModel
      ..jsonData = jsonEncode(response.toJson())
      ..lastUpdated = DateTime.now();
  }
}