import 'dart:convert';
import '../models/cache_models.dart';
import '../models/skills_response.dart';

class SkillsMapper {
  static SkillsCacheModel toModel(SkillsResponse response) {
    final now = DateTime.now();
    return SkillsCacheModel(
      "skills",
      jsonEncode(response.toJson()),
      now,
      now,
    );
  }

  static SkillsResponse toResponse(SkillsCacheModel model) {
    final jsonData = jsonDecode(model.jsonData) as Map<String, dynamic>;
    return SkillsResponse.fromJson(jsonData);
  }

  static SkillsCacheModel updateModel({
    required SkillsCacheModel existingModel,
    required SkillsResponse response,
  }) {
    return existingModel
      ..jsonData = jsonEncode(response.toJson())
      ..lastUpdated = DateTime.now();
  }
}