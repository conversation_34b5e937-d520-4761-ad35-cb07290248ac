import 'dart:convert';
import '../models/cache_models.dart';
import '../models/leave_response.dart';

class LeaveMapper {
  static LeaveCacheModel toModel(LeaveResponse response) {
    final now = DateTime.now();
    return LeaveCacheModel(
      "leave",
      jsonEncode(response.toJson()),
      now,
      now,
    );
  }

  static LeaveResponse toResponse(LeaveCacheModel model) {
    final jsonData = jsonDecode(model.jsonData) as Map<String, dynamic>;
    return LeaveResponse.fromJson(jsonData);
  }

  static LeaveCacheModel updateModel({
    required LeaveCacheModel existingModel,
    required LeaveResponse response,
  }) {
    return existingModel
      ..jsonData = jsonEncode(response.toJson())
      ..lastUpdated = DateTime.now();
  }
}