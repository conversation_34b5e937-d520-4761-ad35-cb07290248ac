import 'dart:convert';
import '../models/cache_models.dart';
import '../models/availability_response.dart';

class AvailabilityMapper {
  static AvailabilityCacheModel toModel(AvailabilityResponse response) {
    final now = DateTime.now();
    return AvailabilityCacheModel(
      "availability",
      jsonEncode(response.toJson()),
      now,
      now,
    );
  }

  static AvailabilityResponse toResponse(AvailabilityCacheModel model) {
    final jsonData = jsonDecode(model.jsonData) as Map<String, dynamic>;
    return AvailabilityResponse.fromJson(jsonData);
  }

  static AvailabilityCacheModel updateModel({
    required AvailabilityCacheModel existingModel,
    required AvailabilityResponse response,
  }) {
    return existingModel
      ..jsonData = jsonEncode(response.toJson())
      ..lastUpdated = DateTime.now();
  }
}