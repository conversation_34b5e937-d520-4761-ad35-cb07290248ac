import 'dart:convert';
import '../models/cache_models.dart';
import '../models/history_response.dart';

class HistoryMapper {
  static HistoryCacheModel toModel(HistoryResponse response) {
    final now = DateTime.now();
    return HistoryCacheModel(
      "history",
      jsonEncode(response.toJson()),
      now,
      now,
    );
  }

  static HistoryResponse toResponse(HistoryCacheModel model) {
    final jsonData = jsonDecode(model.jsonData) as Map<String, dynamic>;
    return HistoryResponse.fromJson(jsonData);
  }

  static HistoryCacheModel updateModel({
    required HistoryCacheModel existingModel,
    required HistoryResponse response,
  }) {
    return existingModel
      ..jsonData = jsonEncode(response.toJson())
      ..lastUpdated = DateTime.now();
  }
}