import 'dart:convert';
import '../models/cache_models.dart';
import '../models/notification_response.dart';

class <PERSON>ertMapper {
  static AlertCacheModel toModel(NotificationResponse response) {
    final now = DateTime.now();
    return AlertCacheModel(
      "alerts",
      jsonEncode({
        'data': {
          'alerts': response.data.alerts.map((alert) => {
            'alert_id': alert.alertId,
            'client_id': alert.clientId,
            'user_id': alert.userId,
            'title': alert.title,
            'short_description': alert.shortDescription,
            'comment': alert.comment,
            'sender': alert.sender,
            'date': alert.date.toIso8601String(),
            'client_logo_url': alert.clientLogoUrl,
            'is_read': alert.isRead,
            'client_name': alert.clientName,
            'store_name': alert.storeName,
            'store_address': alert.storeAddress,
            'store_suburb': alert.storeSuburb,
            'store_postcode': alert.storePostcode,
            'task_id': alert.taskId,
            'task_duration': alert.taskDuration,
            'cycle_name': alert.cycleName,
          }).toList(),
        }
      }),
      now,
      now,
    );
  }

  static NotificationResponse toResponse(AlertCacheModel model) {
    final jsonData = jsonDecode(model.jsonData) as Map<String, dynamic>;
    return NotificationResponse.fromJson(jsonData);
  }

  static AlertCacheModel updateModel({
    required AlertCacheModel existingModel,
    required NotificationResponse response,
  }) {
    return existingModel
      ..jsonData = jsonEncode({
        'data': {
          'alerts': response.data.alerts.map((alert) => {
            'alert_id': alert.alertId,
            'client_id': alert.clientId,
            'user_id': alert.userId,
            'title': alert.title,
            'short_description': alert.shortDescription,
            'comment': alert.comment,
            'sender': alert.sender,
            'date': alert.date.toIso8601String(),
            'client_logo_url': alert.clientLogoUrl,
            'is_read': alert.isRead,
            'client_name': alert.clientName,
            'store_name': alert.storeName,
            'store_address': alert.storeAddress,
            'store_suburb': alert.storeSuburb,
            'store_postcode': alert.storePostcode,
            'task_id': alert.taskId,
            'task_duration': alert.taskDuration,
            'cycle_name': alert.cycleName,
          }).toList(),
        }
      })
      ..lastUpdated = DateTime.now();
  }
}