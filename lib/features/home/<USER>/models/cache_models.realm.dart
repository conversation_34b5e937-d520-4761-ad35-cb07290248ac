// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cache_models.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// ignore_for_file: type=lint
class HistoryCacheModel extends _HistoryCacheModel
    with RealmEntity, RealmObjectBase, RealmObject {
  HistoryCacheModel(
    String id,
    String jsonData,
    DateTime lastUpdated,
    DateTime createdAt,
  ) {
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set(this, 'jsonData', jsonData);
    RealmObjectBase.set(this, 'lastUpdated', lastUpdated);
    RealmObjectBase.set(this, 'createdAt', createdAt);
  }

  HistoryCacheModel._();

  @override
  String get id => RealmObjectBase.get<String>(this, 'id') as String;
  @override
  set id(String value) => RealmObjectBase.set(this, 'id', value);

  @override
  String get jsonData =>
      RealmObjectBase.get<String>(this, 'jsonData') as String;
  @override
  set jsonData(String value) => RealmObjectBase.set(this, 'jsonData', value);

  @override
  DateTime get lastUpdated =>
      RealmObjectBase.get<DateTime>(this, 'lastUpdated') as DateTime;
  @override
  set lastUpdated(DateTime value) =>
      RealmObjectBase.set(this, 'lastUpdated', value);

  @override
  DateTime get createdAt =>
      RealmObjectBase.get<DateTime>(this, 'createdAt') as DateTime;
  @override
  set createdAt(DateTime value) =>
      RealmObjectBase.set(this, 'createdAt', value);

  @override
  Stream<RealmObjectChanges<HistoryCacheModel>> get changes =>
      RealmObjectBase.getChanges<HistoryCacheModel>(this);

  @override
  Stream<RealmObjectChanges<HistoryCacheModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<HistoryCacheModel>(this, keyPaths);

  @override
  HistoryCacheModel freeze() =>
      RealmObjectBase.freezeObject<HistoryCacheModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'jsonData': jsonData.toEJson(),
      'lastUpdated': lastUpdated.toEJson(),
      'createdAt': createdAt.toEJson(),
    };
  }

  static EJsonValue _toEJson(HistoryCacheModel value) => value.toEJson();
  static HistoryCacheModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
        'jsonData': EJsonValue jsonData,
        'lastUpdated': EJsonValue lastUpdated,
        'createdAt': EJsonValue createdAt,
      } =>
        HistoryCacheModel(
          fromEJson(id),
          fromEJson(jsonData),
          fromEJson(lastUpdated),
          fromEJson(createdAt),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(HistoryCacheModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, HistoryCacheModel, 'HistoryCacheModel', [
      SchemaProperty('id', RealmPropertyType.string, primaryKey: true),
      SchemaProperty('jsonData', RealmPropertyType.string),
      SchemaProperty('lastUpdated', RealmPropertyType.timestamp),
      SchemaProperty('createdAt', RealmPropertyType.timestamp),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class AlertCacheModel extends _AlertCacheModel
    with RealmEntity, RealmObjectBase, RealmObject {
  AlertCacheModel(
    String id,
    String jsonData,
    DateTime lastUpdated,
    DateTime createdAt,
  ) {
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set(this, 'jsonData', jsonData);
    RealmObjectBase.set(this, 'lastUpdated', lastUpdated);
    RealmObjectBase.set(this, 'createdAt', createdAt);
  }

  AlertCacheModel._();

  @override
  String get id => RealmObjectBase.get<String>(this, 'id') as String;
  @override
  set id(String value) => RealmObjectBase.set(this, 'id', value);

  @override
  String get jsonData =>
      RealmObjectBase.get<String>(this, 'jsonData') as String;
  @override
  set jsonData(String value) => RealmObjectBase.set(this, 'jsonData', value);

  @override
  DateTime get lastUpdated =>
      RealmObjectBase.get<DateTime>(this, 'lastUpdated') as DateTime;
  @override
  set lastUpdated(DateTime value) =>
      RealmObjectBase.set(this, 'lastUpdated', value);

  @override
  DateTime get createdAt =>
      RealmObjectBase.get<DateTime>(this, 'createdAt') as DateTime;
  @override
  set createdAt(DateTime value) =>
      RealmObjectBase.set(this, 'createdAt', value);

  @override
  Stream<RealmObjectChanges<AlertCacheModel>> get changes =>
      RealmObjectBase.getChanges<AlertCacheModel>(this);

  @override
  Stream<RealmObjectChanges<AlertCacheModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<AlertCacheModel>(this, keyPaths);

  @override
  AlertCacheModel freeze() =>
      RealmObjectBase.freezeObject<AlertCacheModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'jsonData': jsonData.toEJson(),
      'lastUpdated': lastUpdated.toEJson(),
      'createdAt': createdAt.toEJson(),
    };
  }

  static EJsonValue _toEJson(AlertCacheModel value) => value.toEJson();
  static AlertCacheModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
        'jsonData': EJsonValue jsonData,
        'lastUpdated': EJsonValue lastUpdated,
        'createdAt': EJsonValue createdAt,
      } =>
        AlertCacheModel(
          fromEJson(id),
          fromEJson(jsonData),
          fromEJson(lastUpdated),
          fromEJson(createdAt),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(AlertCacheModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, AlertCacheModel, 'AlertCacheModel', [
      SchemaProperty('id', RealmPropertyType.string, primaryKey: true),
      SchemaProperty('jsonData', RealmPropertyType.string),
      SchemaProperty('lastUpdated', RealmPropertyType.timestamp),
      SchemaProperty('createdAt', RealmPropertyType.timestamp),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class AvailabilityCacheModel extends _AvailabilityCacheModel
    with RealmEntity, RealmObjectBase, RealmObject {
  AvailabilityCacheModel(
    String id,
    String jsonData,
    DateTime lastUpdated,
    DateTime createdAt,
  ) {
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set(this, 'jsonData', jsonData);
    RealmObjectBase.set(this, 'lastUpdated', lastUpdated);
    RealmObjectBase.set(this, 'createdAt', createdAt);
  }

  AvailabilityCacheModel._();

  @override
  String get id => RealmObjectBase.get<String>(this, 'id') as String;
  @override
  set id(String value) => RealmObjectBase.set(this, 'id', value);

  @override
  String get jsonData =>
      RealmObjectBase.get<String>(this, 'jsonData') as String;
  @override
  set jsonData(String value) => RealmObjectBase.set(this, 'jsonData', value);

  @override
  DateTime get lastUpdated =>
      RealmObjectBase.get<DateTime>(this, 'lastUpdated') as DateTime;
  @override
  set lastUpdated(DateTime value) =>
      RealmObjectBase.set(this, 'lastUpdated', value);

  @override
  DateTime get createdAt =>
      RealmObjectBase.get<DateTime>(this, 'createdAt') as DateTime;
  @override
  set createdAt(DateTime value) =>
      RealmObjectBase.set(this, 'createdAt', value);

  @override
  Stream<RealmObjectChanges<AvailabilityCacheModel>> get changes =>
      RealmObjectBase.getChanges<AvailabilityCacheModel>(this);

  @override
  Stream<RealmObjectChanges<AvailabilityCacheModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<AvailabilityCacheModel>(this, keyPaths);

  @override
  AvailabilityCacheModel freeze() =>
      RealmObjectBase.freezeObject<AvailabilityCacheModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'jsonData': jsonData.toEJson(),
      'lastUpdated': lastUpdated.toEJson(),
      'createdAt': createdAt.toEJson(),
    };
  }

  static EJsonValue _toEJson(AvailabilityCacheModel value) => value.toEJson();
  static AvailabilityCacheModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
        'jsonData': EJsonValue jsonData,
        'lastUpdated': EJsonValue lastUpdated,
        'createdAt': EJsonValue createdAt,
      } =>
        AvailabilityCacheModel(
          fromEJson(id),
          fromEJson(jsonData),
          fromEJson(lastUpdated),
          fromEJson(createdAt),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(AvailabilityCacheModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(ObjectType.realmObject, AvailabilityCacheModel,
        'AvailabilityCacheModel', [
      SchemaProperty('id', RealmPropertyType.string, primaryKey: true),
      SchemaProperty('jsonData', RealmPropertyType.string),
      SchemaProperty('lastUpdated', RealmPropertyType.timestamp),
      SchemaProperty('createdAt', RealmPropertyType.timestamp),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class LeaveCacheModel extends _LeaveCacheModel
    with RealmEntity, RealmObjectBase, RealmObject {
  LeaveCacheModel(
    String id,
    String jsonData,
    DateTime lastUpdated,
    DateTime createdAt,
  ) {
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set(this, 'jsonData', jsonData);
    RealmObjectBase.set(this, 'lastUpdated', lastUpdated);
    RealmObjectBase.set(this, 'createdAt', createdAt);
  }

  LeaveCacheModel._();

  @override
  String get id => RealmObjectBase.get<String>(this, 'id') as String;
  @override
  set id(String value) => RealmObjectBase.set(this, 'id', value);

  @override
  String get jsonData =>
      RealmObjectBase.get<String>(this, 'jsonData') as String;
  @override
  set jsonData(String value) => RealmObjectBase.set(this, 'jsonData', value);

  @override
  DateTime get lastUpdated =>
      RealmObjectBase.get<DateTime>(this, 'lastUpdated') as DateTime;
  @override
  set lastUpdated(DateTime value) =>
      RealmObjectBase.set(this, 'lastUpdated', value);

  @override
  DateTime get createdAt =>
      RealmObjectBase.get<DateTime>(this, 'createdAt') as DateTime;
  @override
  set createdAt(DateTime value) =>
      RealmObjectBase.set(this, 'createdAt', value);

  @override
  Stream<RealmObjectChanges<LeaveCacheModel>> get changes =>
      RealmObjectBase.getChanges<LeaveCacheModel>(this);

  @override
  Stream<RealmObjectChanges<LeaveCacheModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<LeaveCacheModel>(this, keyPaths);

  @override
  LeaveCacheModel freeze() =>
      RealmObjectBase.freezeObject<LeaveCacheModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'jsonData': jsonData.toEJson(),
      'lastUpdated': lastUpdated.toEJson(),
      'createdAt': createdAt.toEJson(),
    };
  }

  static EJsonValue _toEJson(LeaveCacheModel value) => value.toEJson();
  static LeaveCacheModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
        'jsonData': EJsonValue jsonData,
        'lastUpdated': EJsonValue lastUpdated,
        'createdAt': EJsonValue createdAt,
      } =>
        LeaveCacheModel(
          fromEJson(id),
          fromEJson(jsonData),
          fromEJson(lastUpdated),
          fromEJson(createdAt),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(LeaveCacheModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, LeaveCacheModel, 'LeaveCacheModel', [
      SchemaProperty('id', RealmPropertyType.string, primaryKey: true),
      SchemaProperty('jsonData', RealmPropertyType.string),
      SchemaProperty('lastUpdated', RealmPropertyType.timestamp),
      SchemaProperty('createdAt', RealmPropertyType.timestamp),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class SkillsCacheModel extends _SkillsCacheModel
    with RealmEntity, RealmObjectBase, RealmObject {
  SkillsCacheModel(
    String id,
    String jsonData,
    DateTime lastUpdated,
    DateTime createdAt,
  ) {
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set(this, 'jsonData', jsonData);
    RealmObjectBase.set(this, 'lastUpdated', lastUpdated);
    RealmObjectBase.set(this, 'createdAt', createdAt);
  }

  SkillsCacheModel._();

  @override
  String get id => RealmObjectBase.get<String>(this, 'id') as String;
  @override
  set id(String value) => RealmObjectBase.set(this, 'id', value);

  @override
  String get jsonData =>
      RealmObjectBase.get<String>(this, 'jsonData') as String;
  @override
  set jsonData(String value) => RealmObjectBase.set(this, 'jsonData', value);

  @override
  DateTime get lastUpdated =>
      RealmObjectBase.get<DateTime>(this, 'lastUpdated') as DateTime;
  @override
  set lastUpdated(DateTime value) =>
      RealmObjectBase.set(this, 'lastUpdated', value);

  @override
  DateTime get createdAt =>
      RealmObjectBase.get<DateTime>(this, 'createdAt') as DateTime;
  @override
  set createdAt(DateTime value) =>
      RealmObjectBase.set(this, 'createdAt', value);

  @override
  Stream<RealmObjectChanges<SkillsCacheModel>> get changes =>
      RealmObjectBase.getChanges<SkillsCacheModel>(this);

  @override
  Stream<RealmObjectChanges<SkillsCacheModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<SkillsCacheModel>(this, keyPaths);

  @override
  SkillsCacheModel freeze() =>
      RealmObjectBase.freezeObject<SkillsCacheModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'jsonData': jsonData.toEJson(),
      'lastUpdated': lastUpdated.toEJson(),
      'createdAt': createdAt.toEJson(),
    };
  }

  static EJsonValue _toEJson(SkillsCacheModel value) => value.toEJson();
  static SkillsCacheModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
        'jsonData': EJsonValue jsonData,
        'lastUpdated': EJsonValue lastUpdated,
        'createdAt': EJsonValue createdAt,
      } =>
        SkillsCacheModel(
          fromEJson(id),
          fromEJson(jsonData),
          fromEJson(lastUpdated),
          fromEJson(createdAt),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(SkillsCacheModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, SkillsCacheModel, 'SkillsCacheModel', [
      SchemaProperty('id', RealmPropertyType.string, primaryKey: true),
      SchemaProperty('jsonData', RealmPropertyType.string),
      SchemaProperty('lastUpdated', RealmPropertyType.timestamp),
      SchemaProperty('createdAt', RealmPropertyType.timestamp),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class InductionCacheModel extends _InductionCacheModel
    with RealmEntity, RealmObjectBase, RealmObject {
  InductionCacheModel(
    String id,
    String jsonData,
    DateTime lastUpdated,
    DateTime createdAt,
  ) {
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set(this, 'jsonData', jsonData);
    RealmObjectBase.set(this, 'lastUpdated', lastUpdated);
    RealmObjectBase.set(this, 'createdAt', createdAt);
  }

  InductionCacheModel._();

  @override
  String get id => RealmObjectBase.get<String>(this, 'id') as String;
  @override
  set id(String value) => RealmObjectBase.set(this, 'id', value);

  @override
  String get jsonData =>
      RealmObjectBase.get<String>(this, 'jsonData') as String;
  @override
  set jsonData(String value) => RealmObjectBase.set(this, 'jsonData', value);

  @override
  DateTime get lastUpdated =>
      RealmObjectBase.get<DateTime>(this, 'lastUpdated') as DateTime;
  @override
  set lastUpdated(DateTime value) =>
      RealmObjectBase.set(this, 'lastUpdated', value);

  @override
  DateTime get createdAt =>
      RealmObjectBase.get<DateTime>(this, 'createdAt') as DateTime;
  @override
  set createdAt(DateTime value) =>
      RealmObjectBase.set(this, 'createdAt', value);

  @override
  Stream<RealmObjectChanges<InductionCacheModel>> get changes =>
      RealmObjectBase.getChanges<InductionCacheModel>(this);

  @override
  Stream<RealmObjectChanges<InductionCacheModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<InductionCacheModel>(this, keyPaths);

  @override
  InductionCacheModel freeze() =>
      RealmObjectBase.freezeObject<InductionCacheModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'jsonData': jsonData.toEJson(),
      'lastUpdated': lastUpdated.toEJson(),
      'createdAt': createdAt.toEJson(),
    };
  }

  static EJsonValue _toEJson(InductionCacheModel value) => value.toEJson();
  static InductionCacheModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
        'jsonData': EJsonValue jsonData,
        'lastUpdated': EJsonValue lastUpdated,
        'createdAt': EJsonValue createdAt,
      } =>
        InductionCacheModel(
          fromEJson(id),
          fromEJson(jsonData),
          fromEJson(lastUpdated),
          fromEJson(createdAt),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(InductionCacheModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, InductionCacheModel, 'InductionCacheModel', [
      SchemaProperty('id', RealmPropertyType.string, primaryKey: true),
      SchemaProperty('jsonData', RealmPropertyType.string),
      SchemaProperty('lastUpdated', RealmPropertyType.timestamp),
      SchemaProperty('createdAt', RealmPropertyType.timestamp),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
