import 'package:realm/realm.dart';

part 'cache_models.realm.dart';

// History Cache Model (stores JSON)
@RealmModel()
class _HistoryCacheModel {
  @PrimaryKey()
  late String id; // "history"
  
  late String jsonData; // Serialized HistoryResponse JSON
  late DateTime lastUpdated;
  late DateTime createdAt;
}

// Alert Cache Model (stores JSON)
@RealmModel()
class _AlertCacheModel {
  @PrimaryKey()
  late String id; // "alerts"
  
  late String jsonData; // Serialized NotificationResponse JSON
  late DateTime lastUpdated;
  late DateTime createdAt;
}

// Availability Cache Model (stores JSON)
@RealmModel()
class _AvailabilityCacheModel {
  @PrimaryKey()
  late String id; // "availability"
  
  late String jsonData; // Serialized AvailabilityResponse JSON
  late DateTime lastUpdated;
  late DateTime createdAt;
}

// Leave Cache Model (stores JSON)
@RealmModel()
class _LeaveCacheModel {
  @PrimaryKey()
  late String id; // "leave"
  
  late String jsonData; // Serialized LeaveResponse JSON
  late DateTime lastUpdated;
  late DateTime createdAt;
}

// Skills Cache Model (stores JSON)
@RealmModel()
class _SkillsCacheModel {
  @PrimaryKey()
  late String id; // "skills"
  
  late String jsonData; // Serialized SkillsResponse JSON
  late DateTime lastUpdated;
  late DateTime createdAt;
}

// Induction Cache Model (stores JSON)
@RealmModel()
class _InductionCacheModel {
  @PrimaryKey()
  late String id; // "induction"
  
  late String jsonData; // Serialized InductionResponse JSON
  late DateTime lastUpdated;
  late DateTime createdAt;
}