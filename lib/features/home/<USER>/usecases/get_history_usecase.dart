import 'package:storetrack_app/shared/models/result.dart';
import '../../data/repositories/home_repository.dart';
import '../../data/models/history_response.dart';

class GetHistoryUseCase {
  final HomeRepository repository;

  GetHistoryUseCase(this.repository);

  Future<Result<HistoryResponse>> call({
    required String token,
    required String userId,
    bool isSync = false,
  }) async {
    // Try cache first if not syncing
    if (!isSync) {
      final cached = await repository.getCachedHistory();
      if (cached != null) {
        return Result.success(cached);
      }
    }
    
    // Fetch from remote using the provided userId
    final result = await repository.getHistory(
      token: token,
      userId: userId,
    );
    
    // Cache the result if successful (overwrites existing cache)
    if (result.isSuccess && result.data != null) {
      await repository.saveHistory(result.data!);
    }
    
    return result;
  }
}
