import 'package:storetrack_app/shared/models/result.dart';
import '../entities/misc_setting_response_entity.dart';
import '../../data/repositories/home_repository.dart';

class GetMiscSettingUseCase {
  final HomeRepository repository;

  GetMiscSettingUseCase(this.repository);

  Future<Result<MiscSettingResponseEntity>> call({
    required String token,
    required String userId,
  }) async {
    return await repository.getMiscSetting(
      token: token,
      userId: userId,
    );
  }
}
