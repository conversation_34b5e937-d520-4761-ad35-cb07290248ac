part of 'emulate_user_cubit.dart';

abstract class Emulate<PERSON>serState extends Equatable {
  const EmulateUserState();

  @override
  List<Object?> get props => [];
}

class EmulateUserInitial extends EmulateUserState {}

class EmulateUserLoading extends EmulateUserState {}

class EmulateUserLoaded extends EmulateUserState {
  final List<EmulateUserEntity> users;
  final List<EmulateUserEntity> filteredUsers;
  final String searchQuery;

  const EmulateUserLoaded({
    required this.users,
    required this.filteredUsers,
    this.searchQuery = '',
  });

  @override
  List<Object?> get props => [users, filteredUsers, searchQuery];

  EmulateUserLoaded copyWith({
    List<EmulateUserEntity>? users,
    List<EmulateUserEntity>? filteredUsers,
    String? searchQuery,
  }) {
    return EmulateUserLoaded(
      users: users ?? this.users,
      filteredUsers: filteredUsers ?? this.filteredUsers,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

class EmulateUserError extends EmulateUserState {
  final String message;

  const EmulateUserError(this.message);

  @override
  List<Object?> get props => [message];
}
