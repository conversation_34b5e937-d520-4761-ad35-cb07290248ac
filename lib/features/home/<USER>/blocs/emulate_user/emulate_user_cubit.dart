import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';
import '../../../domain/entities/emulate_user_response_entity.dart';
import '../../../domain/usecases/get_emulate_users_usecase.dart';

part 'emulate_user_state.dart';

class EmulateUserCubit extends Cubit<EmulateUserState> {
  final GetEmulateUsersUseCase _getEmulateUsersUseCase;

  EmulateUserCubit({
    required GetEmulateUsersUseCase getEmulateUsersUseCase,
  })  : _getEmulateUsersUseCase = getEmulateUsersUseCase,
        super(EmulateUserInitial());

  Future<void> fetchEmulateUsers() async {
    emit(EmulateUserLoading());

    try {
      final dataManager = sl<DataManager>();
      const token = "Y6lI9gCoCzFdS0WdoR6v6x1vxnmstbN6";
      final userId = await dataManager.getUserId();

      if (userId == null) {
        emit(const EmulateUserError(
            'Authentication error. Please login again.'));
        return;
      }

      final result = await _getEmulateUsersUseCase.call(
        token: token,
        userId: userId,
      );

      if (result.isSuccess && result.data != null) {
        final users = result.data!.data?.activeUsersInfo ?? [];
        emit(EmulateUserLoaded(
          users: users,
          filteredUsers: users,
        ));
      } else {
        emit(EmulateUserError(result.error ?? 'Failed to fetch emulate users'));
      }
    } catch (e) {
      emit(EmulateUserError('Error fetching emulate users: $e'));
    }
  }

  void searchUsers(String query) {
    final currentState = state;
    if (currentState is EmulateUserLoaded) {
      if (query.isEmpty) {
        emit(currentState.copyWith(
          filteredUsers: currentState.users,
          searchQuery: query,
        ));
      } else {
        final filteredUsers = currentState.users.where((user) {
          return user.displayName.toLowerCase().contains(query.toLowerCase()) ||
              (user.email?.toLowerCase().contains(query.toLowerCase()) ??
                  false) ||
              (user.state?.toLowerCase().contains(query.toLowerCase()) ??
                  false) ||
              (user.contractor?.toLowerCase().contains(query.toLowerCase()) ??
                  false);
        }).toList();

        emit(currentState.copyWith(
          filteredUsers: filteredUsers,
          searchQuery: query,
        ));
      }
    }
  }

  void clearSearch() {
    final currentState = state;
    if (currentState is EmulateUserLoaded) {
      emit(currentState.copyWith(
        filteredUsers: currentState.users,
        searchQuery: '',
      ));
    }
  }

  void resetState() {
    emit(EmulateUserInitial());
  }
}
