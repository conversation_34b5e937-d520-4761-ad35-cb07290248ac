import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/core/constants/app_constants.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_fonts.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/unschedule/unschedule_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/unschedule/unschedule_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';

@RoutePage()
class UnscheduledPosTasksPage extends StatefulWidget {
  const UnscheduledPosTasksPage({super.key});

  @override
  State<UnscheduledPosTasksPage> createState() =>
      _UnscheduledPosTasksPageState();
}

class _UnscheduledPosTasksPageState extends State<UnscheduledPosTasksPage> {
  String actualDeviceUid = '';
  late String actualUserId;
  final String actualAppVersion = AppConstants.appVersion;
  late String actualUserToken;

  List<TaskDetail> posRequiredTasks = [];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    try {
      // Get user ID, token, and device ID from DataManager
      final dataManager = sl<DataManager>();
      actualUserId = await dataManager.getUserId() ?? "0";
      actualUserToken = await dataManager.getAuthToken() ?? "0";
      actualDeviceUid = await dataManager.getOrCreateDeviceId();

      // Fetch unscheduled tasks
      if (mounted) {
        context.read<UnscheduleTaskCubit>().getData(
              TasksRequestEntity(
                deviceUid: actualDeviceUid,
                userId: actualUserId,
                appversion: actualAppVersion,
                tasks: const [],
                token: actualUserToken,
              ),
            );
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to initialize: ${e.toString()}',
        );
      }
    }
  }

  void _filterPosRequiredTasks(List<TaskDetail> allTasks) {
    posRequiredTasks = allTasks
        .where((task) =>
            task.posRequired == true &&
            (task.taskStatus == "Tentative" ||
                task.taskStatus == "Confirmed") &&
            task.taskId != 0 &&
            task.isOpen == false)
        .toList()
      ..sort((a, b) => (a.storeName ?? '').compareTo(b.storeName ?? ''));
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<UnscheduleTaskCubit, UnscheduleTaskState>(
      listener: (context, state) {
        if (state is UnscheduleTaskSuccess) {
          setState(() {
            var tasksResponse = state.tasksResponse;
            List<TaskDetail> allTasks = tasksResponse.addTasks ?? [];
            _filterPosRequiredTasks(allTasks);
          });
        } else if (state is UnscheduleTaskError) {
          SnackBarService.error(
            context: context,
            message: state.message,
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: AppColors.lightGrey1,
          appBar: CustomAppBar(
            title: 'POS Tasks',
            onBackPressed: () => Navigator.pop(context),
          ),
          body: state is UnscheduleTaskLoading
              ? const Center(child: CircularProgressIndicator())
              : state is UnscheduleTaskError
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.error_outline,
                            color: Colors.red,
                            size: 48,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error loading POS tasks',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: 8),
                          ElevatedButton(
                            onPressed: _initializeData,
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    )
                  : RefreshIndicator(
                      onRefresh: _initializeData,
                      child: _buildTasksList(),
                    ),
        );
      },
    );
  }

  Widget _buildTasksList() {
    if (posRequiredTasks.isEmpty) {
      return const Center(
        child: EmptyState(message: 'No POS tasks available'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: posRequiredTasks.length,
      itemBuilder: (context, index) {
        final task = posRequiredTasks[index];
        return _buildTaskCard(task);
      },
    );
  }

  Widget _buildTaskCard(TaskDetail task) {
    final textTheme = Theme.of(context).textTheme;
    final bool isConfirmed = task.taskStatus == "Confirmed";
    final Color statusColor =
        isConfirmed ? AppColors.loginGreen : AppColors.warningTextColor;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border(
          left: BorderSide(
            color: statusColor,
            width: 3,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _onTaskTap(task),
        borderRadius: BorderRadius.circular(10),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row with Store Name
              Row(
                children: [
                  Expanded(
                    child: Text(
                      task.storeName ?? 'Unknown Store',
                      style: textTheme.montserratSemiBold.copyWith(
                        fontSize: 16,
                        color: AppColors.black,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // Right side icons
                  Row(
                    children: [
                      // Address Icon (Store/Home)
                      if (task.location != null && task.location!.isNotEmpty)
                        Container(
                          margin: const EdgeInsets.only(right: 8),
                          child: const Icon(
                            Icons.store,
                            color: AppColors.primaryBlue,
                            size: 18,
                          ),
                        ),

                      // Mandatory Task Indicator
                      if (task.isPosMandatory == true)
                        const Icon(
                          Icons.priority_high_rounded,
                          color: AppColors.loginRed,
                          size: 18,
                        ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Client Name
              Text(
                task.client ?? 'Unknown Client',
                style: const TextStyle(
                  fontFamily: AppFonts.montserrat,
                  fontSize: 14,
                  color: AppColors.blackTint1,
                ),
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 8),

              // Bottom Row with Task Cycle and Date Range
              Row(
                children: [
                  // Task Cycle
                  Expanded(
                    child: Text(
                      task.cycle ?? 'No Cycle',
                      style: const TextStyle(
                        fontFamily: AppFonts.montserrat,
                        fontSize: 13,
                        color: AppColors.black,
                        fontWeight: FontWeight.w500,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // Task Date Range
                  Text(
                    _formatDateRange(task),
                    style: const TextStyle(
                      fontFamily: AppFonts.montserrat,
                      fontSize: 13,
                      color: AppColors.blackTint1,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDateRange(TaskDetail task) {
    if (task.rangeStart != null && task.rangeEnd != null) {
      final startDate = DateFormat('MMM dd').format(task.rangeStart!);
      final endDate = DateFormat('MMM dd').format(task.rangeEnd!);
      return '$startDate - $endDate';
    } else if (task.rangeStart != null) {
      return DateFormat('MMM dd').format(task.rangeStart!);
    } else if (task.rangeEnd != null) {
      return DateFormat('MMM dd').format(task.rangeEnd!);
    }
    return 'No Date Range';
  }

  void _onTaskTap(TaskDetail task) {
    // Navigate to POS details page
    context.navigateTo(PosRoute(task: task));
  }
}
